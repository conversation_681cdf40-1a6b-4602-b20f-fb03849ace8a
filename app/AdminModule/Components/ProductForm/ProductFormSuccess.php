<?php declare(strict_types = 1);

namespace App\AdminModule\Components\ProductForm;

use App\AdminModule\Components\ProductForm\Section\Tag\TagHandler;
use App\Model\CustomContent\CustomContent;
use App\Model\CustomField\CustomFields;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\Price;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductModel;
use App\Model\Orm\ProductFile\ProductFile;
use App\Model\Orm\ProductImage\ProductImage;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductLocalization\ProductLocalizationModel;
use App\Model\Orm\ProductProduct\ProductProduct;
use App\Model\Orm\ProductTree\ProductTree;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\ProductVariant\ProductVariantModel;
use App\Model\Orm\ProductVariantPrice\ProductVariantPrice;
use App\Model\Orm\Supply\Supply;
use App\Model\VatCalculator;
use App\PostType\Page\Model\Orm\TreeModel;
use App\Model\Orm\TreeProduct\TreeProduct;
use App\Model\Orm\User\User;
use App\PostType\Tag\Model\Orm\Tag\TagModel;
use App\PostType\Tag\Model\Orm\Tag\TagRepository;
use App\PostType\Tag\Model\TagType;
use Brick\Math\BigDecimal;
use Brick\Money\Money;
use Nette\Application\UI\Form;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use stdClass;
use function str_replace;
use function trim;

final class ProductFormSuccess
{

	public function __construct(
		private readonly Orm $orm,
		private readonly TreeModel $treeModel,
		private readonly ProductLocalizationModel $productLocalizationModel,
		private readonly ProductVariantModel $productVariantModel,
		private readonly CustomFields $customFields,
		private readonly CustomContent $customContent,
		private readonly ProductModel $productModel,
		private readonly TagModel $tagModel,
		private readonly TagRepository $tagRepository,
		private readonly TagHandler $tagHandler,
	)
	{
	}

	/**
	 * @param ICollection<Mutation> $mutations
	 */
	public function execute(Form $form, Product $product, User $user, ArrayHash $values, ICollection $mutations): void
	{
		$data = (array) $form->getHttpData();

		$this->handleCommonProduct($product, $user, $values->productCommon);
		$this->handleLocalizationsProduct($product, $values->productLocalizations, $data);
		$this->handleVariants($product, $values, $user);

		if (isset($data['productCommon']['images'])) {
			$this->handleImagesVariants($product, $data['productCommon']['images']);
		}
		$product = $this->productModel->recalculateProduct($product);
		$this->orm->product->persistAndFlush($product);

		$this->handleParameters($data, $product);

		$this->handleSetups($product, $values);
		$this->handleInternalName($product);
		$this->handleTagReflections($product);
		$this->orm->product->persistAndFlush($product);

		$this->productModel->saveToEs($product);
	}

	private function handleCommonProduct(Product $product, User $userEntity, ArrayHash $commonProductFormData): void
	{
		$product->internalName = $commonProductFormData->internalName;

//		$product->isOld = (int) $commonProductFormData->isOld;
//		$product->isElectronic = (int) $commonProductFormData->isElectronic;
//		$product->isFreeTransport = (int) $commonProductFormData->isFreeTransport;
//		$product->notSoldSeparately = (int) $commonProductFormData->notSoldSeparately;
		if ($commonProductFormData->publicFrom) {
			$product->publicFrom = $commonProductFormData->publicFrom;
		}
		if ($commonProductFormData->publicTo) {
			$product->publicTo = $commonProductFormData->publicTo;
		}
//
//		if ($commonProductFormData->freeTransportForcedFrom) {
//			$product->freeTransportForcedFrom = $commonProductFormData->freeTransportForcedFrom;
//		}
//		if ($commonProductFormData->freeTransportForcedTo) {
//			$product->freeTransportForcedTo = $commonProductFormData->freeTransportForcedTo;
//		}
		$product->edited = $userEntity->id;
		$product->editedTime = new DateTimeImmutable();

		$this->handleProducts($product, $commonProductFormData);
		$this->handleProductsSimilar($product, $commonProductFormData);

		$this->handleImages($product, $commonProductFormData);

		$this->orm->persistAndFlush($product);
	}

	private function handleLocalizationsProduct(Product $product, ArrayHash $localizations, array $data): void
	{
		foreach ($localizations as $mutationId => $localizationData) {
			$localization = $product->productLocalizations->toCollection()->getBy(['mutation->id' => $mutationId]);
			if ($localization) {
				$localization->name = $localizationData->name;
				$localization->annotation = $localizationData->annotation;
				$localization->content = $localizationData->content;
				$localization->nameTitle = $localizationData->nameTitle;
				$localization->nameAnchor = $localizationData->nameAnchor;
				$localization->description = $localizationData->description;
				// save alias history
				$localization->setAliasHistoryString($localizationData->aliasHistory);
				// than solve alais
				$localization->setAlias($localizationData->alias);
				$localization->public = (int) $localizationData->public;

				if (isset($localizationData->categories)) {
					$this->handleCategories($localization, $localizationData->categories);
				}

//				if (isset($localizationData->tags)) {
//					$this->tagHandler->saveTags($localization, $localizationData->tags);
//				}

				if (isset($localizationData->pages)) {
					$this->handlePages($localization, $localizationData->pages);
				}

				if (isset($localizationData->files)) {
					$this->handleFiles($localization, $localizationData->files);
				}

				if (isset($localizationData->cf)) {
					if (isset($localizationData->cf) && $localizationData->cf !== '') {
						$localization->setCf($this->customFields->prepareDataToSave($localizationData->cf));
					} else {
						$localization->setCf(new ArrayHash());
					}
				}

				if (isset($localizationData->cc)) {
					if (isset($localizationData->cc) && $localizationData->cc !== ''
						&& isset($localizationData->ccScheme) && $localizationData->ccScheme !== '') {
						$localization->setCc($this->customContent->prepareDataToSave($localizationData->cc, $localizationData->ccScheme));
					} else {
						$localization->setCc(new ArrayHash());
					}
				}

				$setup = new stdClass();
				$setup->inheritCategories = $localizationData->setup->inheritCategories;
				$setup->inheritFiles = $localizationData->setup->inheritFiles;
				$setup->inheritPages = $localizationData->setup->inheritPages;

				$localization->setup = ArrayHash::from((array) $setup);
			}
		}
	}

	private function handleVariants(Product $product, ArrayHash $values, User $user): void
	{
		$tmpIds = [];

		if (isset($values->variants)) {
			//update items
			if ($values->variants) {
				$sort = 0;
				foreach ($values->variants as $variantId => $variantData) {
					if ($variantId === 'newItemMarker') {
						continue;
					}

					if (is_int($variantId)) {
						$variant = $this->orm->productVariant->getById($variantId);
						$variant->edited = new DateTimeImmutable();
						$variant->editedBy = $user->id;
					} else {
						$variant = $this->productVariantModel->createWithLocalizations($product);
						$variant->created = new DateTimeImmutable();
						$variant->createdBy = $user->id;
					}

					$variant->sort = $sort;
					$sort++;

					$this->handleVariantCommon($variant, $variantData);
					$this->handleVariantLocalizations($variant, $variantData);
					$this->handleVariantPrices($variant, $variantData);
				//	$this->handleVariantSupplies($variant, $variantData);

					$this->orm->productVariant->persistAndFlush($variant);
					$tmpIds[] = $variant->id;
				}
			}
		}

		foreach ($product->variants as $variant) {
			if (!in_array($variant->id, $tmpIds)) {
				$this->orm->productVariant->remove($variant);
			}
		}
	}

	private function handleVariantCommon(ProductVariant $variant, ArrayHash $variantData): void
	{
		if (isset($variantData->variantCommon)) {
			$variant->ean = $variantData->variantCommon->ean;
			$variant->isOld = (bool)$variantData->variantCommon->isOld;
			$variant->isReSale = (bool)$variantData->variantCommon->isReSale;
			$variant->isFreeTransport = (bool)$variantData->variantCommon->isFreeTransport;
			$variant->isFreeTransportForced = (bool)$variantData->variantCommon->isFreeTransportForced;
			if ($variantData->variantCommon->freeTransportForcedFrom) {
				$variant->freeTransportForcedFrom = $variantData->variantCommon->freeTransportForcedFrom;
			}
			if ($variantData->variantCommon->freeTransportForcedTo) {
				$variant->freeTransportForcedTo = $variantData->variantCommon->freeTransportForcedTo;
			}

//			$variant->code = $variantData->variantCommon->code;

//			$variant->param1Value = $variantData->variantCommon->param1Value ?? null;
//			$variant->param2Value = $variantData->variantCommon->param2Value ?? null;
		}
	}

	private function handleVariantLocalizations(ProductVariant $variant, ArrayHash $variantData): void
	{
		if (isset($variantData->variantLocalizations)) {
			foreach ($variantData->variantLocalizations as $mutationId => $variantLocalizationData) {

				$productVariantLocalization = $variant->variantLocalizations->toCollection()->getBy(['mutation->id' => $mutationId]);
				if ($productVariantLocalization) {
					$productVariantLocalization->active = (int) $variantLocalizationData->active;
					$productVariantLocalization->name = (string) $variantLocalizationData->name;
					$productVariantLocalization->nameShort = (string) $variantLocalizationData->nameShort;
				}
			}
		}
	}

	private function handleVariantPrices(ProductVariant $variant, ArrayHash $variantData): void
	{
		if (isset($variantData->variantPrices)) {
			//dumpe($variantData->variantPrices);
			foreach ($variantData->variantPrices as $mutationId => $currencies) {
				$mutation = $this->orm->mutation->getById($mutationId);
				foreach ($currencies as $currency => $variantPrices) {
					foreach ($variantPrices as $priceLevelId => $priceData) {
						$amount = str_replace(',', '.', trim($priceData->price));
						if ($amount === '') {
							$amount = 0;
						}

						if ($priceData->priceVat !== null) {
							$amount = VatCalculator::priceWithoutVat(Money::of($priceData->priceVat, $currency), BigDecimal::of($priceData->vat))->getAmount()->toFloat();
						}

						$price = Price::from(Money::of($amount, $currency));
						//bdump($price);
						if (isset($variant->pricesByLevelCurrency[$mutationId][$priceLevelId][$currency])) {
							$productVariantPrice = $variant->pricesByLevelCurrency[$mutationId][$priceLevelId][$currency];
						} else {
							$productVariantPrice = new ProductVariantPrice();
							$this->orm->productVariantPrice->attach($productVariantPrice);
							$productVariantPrice->mutation = $mutation;
							$productVariantPrice->priceLevel = $priceLevelId;
							$productVariantPrice->productVariant = $variant;
							$productVariantPrice->productId = $variant->product->id;
						}

						$productVariantPrice->price = $price;
						$productVariantPrice->vat = (float) $priceData->vat;
						$productVariantPrice->validFrom = isset($priceData->validFrom) ? new DateTimeImmutable($priceData->validFrom) : null;
						$productVariantPrice->validTo = isset($priceData->validTo) ? new DateTimeImmutable($priceData->validTo) : null;
					}
				}
			}
		}
	}

//	private function handleVariantSupplies(ProductVariant $variant, ArrayHash $variantData): void
//	{
//		if (isset($variantData->variantSupplies)) {
//			foreach ($variantData->variantSupplies as $stockId => $supplyData) {
//				if (isset($variant->suppliesByStock[$stockId])) {
//					$supply = $variant->suppliesByStock[$stockId];
//				} else {
//					$supply = new Supply();
//					$this->orm->supply->attach($supply);
//					$supply->stock = $stockId;
//					$supply->variant = $variant;
//				}
//
//				$supply->amount = (int) $supplyData->amount;
//			}
//		}
//	}

	private function handleProducts(Product $product, ArrayHash $commonProductFormData): void
	{
		$collection = $product->productsAll;
		$containerName = 'products';
		$type = ProductProduct::TYPE_NORMAL;
		if (isset($commonProductFormData->$containerName)) {
			$this->handleRelations($collection, $containerName, $type, $product, $commonProductFormData);
		}
	}

	private function handleProductsSimilar(Product $product, ArrayHash $commonProductFormData): void
	{
		$collection = $product->similarProductsAll;
		$containerName = 'similar';
		$type = ProductProduct::TYPE_SIMILAR;
		if (isset($commonProductFormData->$containerName)) {
			$this->handleRelations($collection, $containerName, $type, $product, $commonProductFormData);
		}
	}

	private function handleCategories(ProductLocalization $productLocalization, ArrayHash $categoriesFormData): void
	{
		$treeIdForLocalization = [];
		foreach ($categoriesFormData as $categoryKey => $categoryFormData) {
			if ($categoryKey !== 'newItemMarker' && $categoryFormData->id) {
				$treeIdForLocalization[] = $categoryFormData->id;
			}
		}

		$this->productLocalizationModel->attachTo($productLocalization, $treeIdForLocalization);
	}

	/**
	 * @param ICollection<Product> $collection
	 */
	private function handleRelations(ICollection $collection, string $name, string $type, Product $product, ArrayHash $commonProductFormData): void
	{
		$attachedProductIds = $collection->fetchPairs('id', null);

		if ($commonProductFormData->$name) {
			$sort = 0;
			foreach ($commonProductFormData->$name as $attachedProductKey => $attachedProductData) {
				if ($attachedProductData->id) {
					$attachedProduct = $this->orm->product->getById($attachedProductData->id);
					if ($attachedProduct) {
						unset($attachedProductIds[$attachedProduct->id]);
						$this->orm->productProduct->replace($product, $attachedProduct, $type, $sort);
						$sort++;
					}
				}
			}
		}

		foreach ($attachedProductIds as $attachedProduct) {

			$treeProductToDelete = $this->orm->productProduct->getBy([
				'type' => $type,
				'mainProduct' => $product,
				'attachedProduct' => $attachedProduct,
			]);
			$this->orm->productProduct->removeAndFlush($treeProductToDelete);
		}
	}

	private function handlePages(ProductLocalization $localization, ArrayHash $pagesData): void
	{
		$actualPages = $localization->pages->fetchPairs('id', null);

		$count = 0;
		foreach ($pagesData as $pageKey => $pageData) {
			if ($pageData->id) {
				$tree = $this->orm->tree->getById($pageData->id);
				if (is_int($pageKey)) {
					unset($actualPages[$pageData->id]);
				}

				$treeProduct = $this->orm->treeProduct->replace($localization->product, $tree, TreeProduct::TYPE_NORMAL_TO_PRODUCT, $count);
				$this->orm->persist($treeProduct);
				$count++;
			}
		}

		// remove
		foreach ($actualPages as $tree) {
			$treeProductToDelete = $this->orm->treeProduct->getBy([
				'type' => TreeProduct::TYPE_NORMAL_TO_PRODUCT,
				'product' => $localization->product,
				'tree' => $tree,
			]);
			$this->orm->treeProduct->remove($treeProductToDelete);
		}
	}

	private function handleFiles(ProductLocalization $localization, ArrayHash $filesData): void
	{
		$filesToDelete = $localization->files->toCollection()->fetchPairs('this->file->id', null);

		$sort = 0;
		foreach ($filesData as $fileKey => $fileDataRow) {
			if ($fileKey === 'newItemMarker') {
				continue;
			}

			if ($fileDataRow->fileId) {
				if (is_int($fileKey)) {
					$productFile = $localization->files->toCollection()->getBy(['this->file->id' => $fileDataRow->fileId]);
					if ($productFile) {
						$productFile->name = $fileDataRow->fileName;
						$productFile->sort = $sort;
						$productFile->size = $productFile->size;
					}

					unset($filesToDelete[$fileDataRow->fileId]);
				} else {
					$file = $this->orm->file->getById($fileDataRow->fileId);
					if ($file) {
						$productFile = new ProductFile();
						$this->orm->productFile->attach($productFile);
						$productFile->name = $fileDataRow->fileName;
						$productFile->sort = $sort;
						$productFile->size = (string) $file->size;
						$productFile->url = $file->url;
						$productFile->file = $file->id;

						$localization->files->add($productFile);
					}
				}

				if (isset($productFile)) {
					$this->orm->persist($productFile);
					$sort++;
				}
			}
		}

		// remove
		foreach ($filesToDelete as $productFile) {
			$this->orm->productFile->remove($productFile);
		}
	}

	private function handleSetups(Product $product, ArrayHash $values): void
	{
		$defaultLocalization = $product->productLocalizations->toCollection()->getBy(['mutation' => $this->orm->mutation->getDefault()]);

		foreach ($product->productLocalizations as $productLocalization) {
			if ($productLocalization->id === $defaultLocalization->id) {
				continue;
			}

			$setup = $productLocalization->setup;

			// inheritPages
			if (isset($setup->inheritPages) && $setup->inheritPages) {
				$newPages = [];
				foreach ($defaultLocalization->pagesAll as $sisterPage) {
					$newPage = $this->treeModel->findMySisterPageInMutations($sisterPage, $productLocalization->mutation);
					if ($newPage) {
						$newPages[] = $newPage;
					}
				}

				$count = 0;
				$newPageIds = [];
				foreach ($newPages as $pageKey => $newPage) {
					$treeProduct = $this->orm->treeProduct->replace($productLocalization->product, $newPage, TreeProduct::TYPE_NORMAL_TO_PRODUCT, $count);
					$this->orm->persist($treeProduct);
					$count++;
					$newPageIds[] = $newPage->id;
				}

				$treeProductsToDelete = $this->orm->treeProduct->findBy([
					'type' => TreeProduct::TYPE_NORMAL_TO_PRODUCT,
					'product' => $productLocalization->product,
					'tree->rootId' => $productLocalization->mutation->rootId,
					'tree!=' => $newPageIds,
				]);

				foreach ($treeProductsToDelete as $treeProductToDelete) {
					$this->orm->treeProduct->removeAndFlush($treeProductToDelete);
				}
			}

			// inheritCategorie
			if (isset($setup->inheritCategories) && $setup->inheritCategories) {
				$defaultCategories = $product->productTrees->toCollection()->findBy([
					'tree->rootId' => $defaultLocalization->mutation->rootId,
				]);
				$currentCategoriesByTreeId = $product->productTrees->toCollection()->findBy([
					'tree->rootId' => $productLocalization->mutation->rootId,
				])->fetchPairs('tree->id', null);

				foreach ($defaultCategories as $defaultCategory) {
					$newPage = $this->treeModel->findMySisterPageInMutations($defaultCategory->tree, $productLocalization->mutation);
					if ($newPage) {
						if (isset($currentCategoriesByTreeId[$newPage->id])) {
							unset($currentCategoriesByTreeId[$newPage->id]);
						} else {
							$newProductTree = new ProductTree();
							$newProductTree->product = $product;
							$newProductTree->tree = $newPage;
							$newProductTree->sort = $defaultCategory->sort;
							$this->orm->productTree->persistAndFlush($newProductTree);

						}
					}
				}

				foreach ($currentCategoriesByTreeId as $item) {
					$this->orm->productTree->removeAndFlush($item);
				}
			}

			if (isset($setup->inheritFiles) && $setup->inheritFiles) {

				$defaultProductFiles = $defaultLocalization->files->toCollection()->fetchPairs('file->id', null);
				$productFiles = $productLocalization->files->toCollection()->fetchPairs('file->id', null);

				foreach ($defaultProductFiles as $fileId => $defaultProductFile) {
					/** @var ProductFile $defaultProductFile */
					if (isset($productFiles[$fileId])) {
						$updatedProductFile = $productFiles[$fileId];
						unset($productFiles[$fileId]);
					} else {
						$updatedProductFile = new ProductFile();
						$this->orm->productFile->attach($updatedProductFile);
						$updatedProductFile->productLocalization = $productLocalization;
					}

					$updatedProductFile->name = $defaultProductFile->name;
					$updatedProductFile->url = $defaultProductFile->url;
					$updatedProductFile->size = $defaultProductFile->size;
					$updatedProductFile->sort = $defaultProductFile->sort;
					$updatedProductFile->file = $defaultProductFile->file;

					$this->orm->persistAndFlush($updatedProductFile);
				}

				$fileIdsToDelete = array_keys($productFiles);
				if ($fileIdsToDelete) {

					$productFilesToDelete = $this->orm->productFile->findBy([
						'productLocalization' => $productLocalization,
						'file->id' => $fileIdsToDelete,
					]);
					foreach ($productFilesToDelete as $item) {
						$this->orm->productFile->removeAndFlush($item);
					}
				}
			}
		}

		$this->orm->persistAndFlush($product);
	}

	private function handleInternalName(Product $product): void
	{
		$defaultLocalization = $product->productLocalizations->toCollection()->getBy(['mutation' => $this->orm->mutation->getDefault()]);
		$product->internalName = $defaultLocalization->name;
		$this->orm->persistAndFlush($product);
	}

	private function handleImages(Product $product, ArrayHash $commonProductFormData): void
	{
		$productImagesToDelete = $product->images->toCollection()->fetchPairs('libraryImage->id', null);
		if (isset($commonProductFormData->images)) {
			$sort = 0;
			foreach ($commonProductFormData->images as $imageKey => $imageData) {
				if ($imageKey === 'newItemMarker') {
					continue;
				}

				$image = $this->orm->libraryImage->getById($imageData->imageId);
				if (is_int($imageKey)) {
					$productImage = $product->images->toCollection()->getBy(['libraryImage->id' => $imageKey]);
					unset($productImagesToDelete[$productImage->libraryImage->id]);
				} else {
					$productImage = new ProductImage();
					$this->orm->productImage->attach($productImage);
				}

				$productImage->libraryImage = $image->id;
				$productImage->sort = $sort;
				$productImage->product = $product;

				$data = [];
				foreach ($product->productLocalizations as $productLocalization) {
					$langCode = $productLocalization->mutation->langCode;
					$data[$langCode]['name'] = $imageData->$langCode->name;
				}

				$productImage->data = $data;

				$sort++;
			}

			foreach ($productImagesToDelete as $productImageToDelete) {
				$this->orm->remove($productImageToDelete);
			}
		}
	}

	private function handleImagesVariants(Product $product, array $images): void
	{
		foreach ($images as $iKey => $image) {
			if ($iKey === 'newItemMarker') {
				continue;
			}

			$productImage = null;
			$origImage = null;
			$imageId = null;

			if (is_int($iKey)) {
				$imageId = $iKey;
				$origImage = $this->orm->libraryImage->getById($iKey);
				$productImage = $product->images->toCollection()->getBy(['libraryImage->id' => $iKey]);
			} elseif (str_contains($iKey, 'newItemMarker_image_')) {
				$imageId = (int) substr($iKey, strlen('newItemMarker_image_'));
				$origImage = $this->orm->libraryImage->getById($imageId);
				$productImage = $product->images->toCollection()->getBy(['libraryImage->id' => $imageId]);
			}

			if ($productImage !== null) {
				if ($origImage !== null) {
					$productImage->libraryImage = $origImage->id;
				} else {
					$productImage->libraryImage = $imageId;
				}

				$productImage->product = $product;

				if (isset($image['variants'])) {
					$productImage->setVariantIds($image['variants']);
				} else {
					$productImage->setVariantIds();
				}
			}
		}
	}

	private function handleParameters(array $data, Product $product): void // @phpstan-ignore-line
	{
		$selectedParameterValuesIds = [];
		if (isset($data['parameterValue'])) {
			foreach ($data['parameterValue'] as $parameterId => $parameterData) {
				$parameter = $this->orm->parameter->getById($parameterId);

				if ($parameter->isSimple) {
					$simpleParameterValue = $parameterData;
					if ($simpleParameterValue !== '') {
						$parameterValue = $parameter->options->toCollection()->getBy(['internalValue' => $simpleParameterValue]);
						if (!$parameterValue) {
							$parameterValue = new ParameterValue();
							$parameterValue->parameter = $parameter;
							$parameterValue->internalValue = $simpleParameterValue;
							$this->orm->parameterValue->persistAndFlush($parameterValue);
							$parameterValue->internalAlias = (string) $parameterValue->id;
							$this->orm->parameterValue->persistAndFlush($parameterValue);
						}

						$selectedParameterValuesIds[] = $parameterValue->id;
						$res = $this->orm->product->addParameterValue($product, $parameterValue);
					}
				} else {
					// select and multiselect
					if (is_array($parameterData)) {
						foreach ($parameterData as $multiselectValueId) {
							$parameterValue = $parameter->options->toCollection()->getById($multiselectValueId);
							if ($parameterValue) {
								$selectedParameterValuesIds[] = $parameterValue->id;
								$this->orm->product->addParameterValue($product, $parameterValue);
							}
						}
					} else {
						$selectValueId = $parameterData;
						$parameterValue = $parameter->options->toCollection()->getById($selectValueId);
						if ($parameterValue) {
							$selectedParameterValuesIds[] = $parameterValue->id;
							$this->orm->product->addParameterValue($product, $parameterValue);
						}
					}
				}
			}
		}

		if (isset($data['parameterValueLoaded'])) {
			foreach ($data['parameterValueLoaded'] as $parameterId => $loaded) {
				$loaded = (bool) ((int) $loaded);
				if (!$loaded) {
					$parameter = $product->getParameterById($parameterId);
					if ($parameter instanceof stdClass) {
						if ($parameter->type === Parameter::TYPE_MULTISELECT) {
							foreach ($parameter->valueObjects as $paramValue) {
								$selectedParameterValuesIds[] = $paramValue->id;
							}
						} elseif ($parameter->type === Parameter::TYPE_SELECT) {
							$selectedParameterValuesIds[] = $parameter->valueObject->id;
						}
					}
				}
			}
		}

		$this->orm->product->removeMissingParameterValuesIds($product, $selectedParameterValuesIds);
	}

	private function handleTagReflections(Product $product): void
	{
		$this->tagModel->checkTagsForProduct($product);
	}

}
