<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Order\Components\OrderItemsForm;

use _PHPStan_a54cdb067\Symfony\Component\Console\Exception\LogicException;
use App\Model\Erp\ErpOrderService;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Orm;
use App\Model\Orm\Price;
use App\Model\Orm\User\User;
use App\Model\VatCalculator;
use Brick\Math\BigDecimal;
use Brick\Money\Money;
use Nette\Application\UI\Form;
use Nette\Utils\ArrayHash;

final readonly class OrderItemsFormSuccess
{

	public function __construct(
		private Orm $orm,
		private ErpOrderService $erpOrderService,
	)
	{
	}

	public function execute(Form $form, Order $order, User $user, ArrayHash $values): void
	{
		$attachedItems = $order->getBuyableOrderItems();
		$attachedItemsIds = [];
		foreach ($attachedItems as $key => $row) {
			$attachedItemsIds[$row->id] = $row;
		}

		foreach ($values->items as $id => $itemFormData) {
			if ($itemFormData->id) {
				$attachedOrderProduct = $this->orm->orderProduct->getById($itemFormData->id);
				if ($attachedOrderProduct) {
					unset($attachedItemsIds[$attachedOrderProduct->id]);
					$attachedOrderProduct->amount = $itemFormData->amount;

					$currency = $order->currency->getCurrencyCode();
					$unitPrice = $itemFormData->unitPrice;
					$unitPriceVat = $itemFormData->unitPriceVat;
					if (!empty($unitPriceVat)) {
						$vat = $attachedOrderProduct->getVatRateValue()->toFloat();
						$amount = VatCalculator::priceWithoutVat(Money::of($unitPriceVat, $currency), BigDecimal::of($vat))->getAmount()->toFloat();
					} elseif (!empty($unitPrice)) {
						$amount = $unitPrice;
					} else {
						throw new LogicException('no_price_provided');
					}
					$attachedOrderProduct->unitPrice = Price::from(Money::of($amount, $currency));;
					$this->orm->persistAndFlush($attachedOrderProduct);

					$this->erpOrderService->updateOrder($order);
				}
			}

			foreach ($attachedItemsIds as $attachedItemsId) {
				$attachedOrderProduct = $this->orm->orderProduct->getById($attachedItemsId);
				$this->orm->productProduct->removeAndFlush($attachedOrderProduct);
			}
		}
	}
}
