{varType App\Model\Orm\Parameter\Parameter $parameter}

<form n:name="form" id="parameterForm">
	{var $props = [
		title: 'Nastavení parametru',
		id: 'settings-main',
		icon: $templates.'/part/icons/cog.svg',
		open: true,
		variant: 'main',
		classes: ['u-mb-xxs'],
		rowMainClass: 'row-main-max',
	]}

	{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
		{block content}

			<div class="grid grid--y-0">
				<div class="grid__cell size--6-12">
					{include $templates.'/part/core/inp.latte' props: [
						input: $form['name'],
						classesLabel: ['title']
					]}
					{include $templates.'/part/core/inp.latte' props: [
						input: $form['type'],
						type: 'select',
						classesLabel: ['title']
					]}
					{if isset($form['extId'])}
						{include $templates.'/part/core/inp.latte' props: [
							input: $form['extId'],
							classesLabel: ['title']
						]}
					{/if}
					{if isset($form['typeSort'])}
						{include $templates.'/part/core/inp.latte' props: [
							input: $form['typeSort'],
							type: select,

							classesLabel: ['title']
						]}
						<div style="margin-bottom: 1rem;margin-top: -1rem;color:red;" n:if="$parameter->pendingSort">Aktuálně probíha indexace řazení. Způsob řazeni aktuálně nejde zmněnit.</div>
						<div style="margin-bottom: 1rem;margin-top: -1rem;color:red;" n:if="(!$parameter->pendingSort && $parameter->typeSort !== null) || true"><a n:href="reindexSort!">Spustit reindex</a></div>
					{/if}
				</div>
				<div class="grid__cell size--6-12">
					{include $templates.'/part/core/inp.latte' props: [
						input: $form['uid'],
						classesLabel: ['title']
					]}

					{if isset($form['isInFilter'])}
						{include $templates.'/part/core/checkbox.latte' props: [
							input: $form['isInFilter'],
							type: 'checkbox',
							classesLabel: ['title']
						]}
						{if $parameter->isLockedForES}
							<div style="margin-bottom: 1rem;margin-top: -1rem;color:red;">Je nutné provést indexaci. Změna se projeví do 24h</div>
						{/if}
					{/if}

					{if isset($form['isInDetail'])}
						{include $templates.'/part/core/checkbox.latte' props: [
							input: $form['isInDetail'],
							type: 'checkbox',
							classesLabel: ['title']
						]}
					{/if}
					{if isset($form['isProtected'])}
						{include $templates.'/part/core/checkbox.latte' props: [
							input: $form['isProtected'],
							type: 'checkbox',
							classesLabel: ['title']
						]}
					{/if}
					{if isset($form['productType'])}
						{include $templates.'/part/core/inp.latte' props: [
						input: $form['productType'],
						type: 'select',
						classesLabel: ['title']
					]}
					{/if}
				</div>
			</div>
			{foreach $form->getComponent('mutations') as $mutationId=>$mutationInputs}

			{/foreach}


		{/block}
	{/embed}

	{var $props = [
		title: 'Překlady parametru',
		id: 'mutations-main',
		icon: $templates.'/part/icons/flag.svg',
		open: true,
		variant: 'main',
		classes: ['u-mb-xxs'],
		rowMainClass: 'row-main-max',
	]}

	{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
		{block content}
			{foreach $form->getComponent('mutations')->getComponents() as $mutationId=>$mutationInputs}
				<div class="title">{$mutations[$mutationId]->langCode}:</div>
				<div class="grid grid--y-0">
					<div class="grid__cell size--2-12">
						{include $templates.'/part/core/inp.latte' props: [
							input: $form['mutations'][$mutationId]['name'],
							classesLabel: ['title']
						]}
					</div>
					<div class="grid__cell size--2-12">
						{include $templates.'/part/core/inp.latte' props: [
							input: $form['mutations'][$mutationId]['tooltip'],
							classesLabel: ['title']
						]}
					</div>
					<div class="grid__cell size--2-12">
						{include $templates.'/part/core/inp.latte' props: [
							input: $form['mutations'][$mutationId]['filterPrefix'],
							classesLabel: ['title']
						]}
					</div>
					<div class="grid__cell size--2-12">
						{include $templates.'/part/core/inp.latte' props: [
							input: $form['mutations'][$mutationId]['filterPostfix'],
							classesLabel: ['title']
						]}
					</div>
					<div class="grid__cell size--2-12" n:if="isset($form['mutations'][$mutationId]['unit'])">
						{include $templates.'/part/core/inp.latte' props: [
							input: $form['mutations'][$mutationId]['unit'],
							classesLabel: ['title']
						]}
					</div>
				</div>
			{/foreach}
		{/block}
	{/embed}
</form>
